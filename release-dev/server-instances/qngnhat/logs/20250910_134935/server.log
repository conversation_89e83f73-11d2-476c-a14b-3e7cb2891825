2025-09-10T13:49:36.242+07:00  INFO 24405 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 24405 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-10T13:49:36.242+07:00  INFO 24405 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-10T13:49:37.037+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.117+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 77 ms. Found 22 JPA repository interfaces.
2025-09-10T13:49:37.128+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.129+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-10T13:49:37.130+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.139+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 10 JPA repository interfaces.
2025-09-10T13:49:37.140+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.143+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-10T13:49:37.197+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.203+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-10T13:49:37.213+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.216+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-10T13:49:37.216+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.221+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-10T13:49:37.225+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.229+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-10T13:49:37.234+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.237+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-10T13:49:37.238+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.238+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-10T13:49:37.238+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.251+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 10 JPA repository interfaces.
2025-09-10T13:49:37.257+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.260+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-10T13:49:37.266+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.271+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-10T13:49:37.271+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.279+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-10T13:49:37.279+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.283+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 4 JPA repository interfaces.
2025-09-10T13:49:37.284+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.284+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-10T13:49:37.284+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.285+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-10T13:49:37.285+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.290+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-10T13:49:37.290+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.292+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-10T13:49:37.292+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.292+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-10T13:49:37.292+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.304+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-09-10T13:49:37.315+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.322+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-10T13:49:37.322+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.325+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-10T13:49:37.325+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.329+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-10T13:49:37.329+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.335+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-10T13:49:37.335+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.339+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-10T13:49:37.339+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.347+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-10T13:49:37.348+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.357+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-10T13:49:37.357+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.372+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-10T13:49:37.372+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.373+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-10T13:49:37.379+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.380+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-10T13:49:37.380+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.387+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-10T13:49:37.389+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.456+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 66 ms. Found 65 JPA repository interfaces.
2025-09-10T13:49:37.456+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.458+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-10T13:49:37.463+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10T13:49:37.477+07:00  INFO 24405 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 4 JPA repository interfaces.
2025-09-10T13:49:37.750+07:00  INFO 24405 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-10T13:49:37.754+07:00  INFO 24405 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-10T13:49:38.184+07:00  WARN 24405 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-10T13:49:38.383+07:00  INFO 24405 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-10T13:49:38.385+07:00  INFO 24405 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-10T13:49:38.398+07:00  INFO 24405 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-10T13:49:38.398+07:00  INFO 24405 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1990 ms
2025-09-10T13:49:38.467+07:00  WARN 24405 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-10T13:49:38.467+07:00  INFO 24405 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-10T13:49:38.572+07:00  INFO 24405 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@5d52a17b
2025-09-10T13:49:38.573+07:00  INFO 24405 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-10T13:49:38.577+07:00  WARN 24405 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-10T13:49:38.577+07:00  INFO 24405 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-10T13:49:38.585+07:00  INFO 24405 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@6bcf2780
2025-09-10T13:49:38.585+07:00  INFO 24405 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-10T13:49:38.585+07:00  WARN 24405 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-10T13:49:38.585+07:00  INFO 24405 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-10T13:49:38.594+07:00  INFO 24405 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2f7af3d0
2025-09-10T13:49:38.594+07:00  INFO 24405 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-10T13:49:38.595+07:00  WARN 24405 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-10T13:49:38.595+07:00  INFO 24405 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-10T13:49:38.603+07:00  INFO 24405 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@4ff7de80
2025-09-10T13:49:38.603+07:00  INFO 24405 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-10T13:49:38.603+07:00  WARN 24405 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-10T13:49:38.603+07:00  INFO 24405 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-10T13:49:38.610+07:00  INFO 24405 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4746b319
2025-09-10T13:49:38.610+07:00  INFO 24405 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-10T13:49:38.610+07:00  INFO 24405 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-10T13:49:38.656+07:00  INFO 24405 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-10T13:49:38.657+07:00  INFO 24405 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@1c93f349{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13369059440323324654/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47620803{STARTED}}
2025-09-10T13:49:38.658+07:00  INFO 24405 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@1c93f349{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13369059440323324654/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47620803{STARTED}}
2025-09-10T13:49:38.659+07:00  INFO 24405 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@40d1e561{STARTING}[12.0.15,sto=0] @3248ms
2025-09-10T13:49:38.760+07:00  INFO 24405 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-10T13:49:38.789+07:00  INFO 24405 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-10T13:49:38.804+07:00  INFO 24405 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-10T13:49:38.927+07:00  INFO 24405 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-10T13:49:38.965+07:00  WARN 24405 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-10T13:49:39.628+07:00  INFO 24405 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-10T13:49:39.636+07:00  INFO 24405 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4a9cd434] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-10T13:49:39.813+07:00  INFO 24405 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-10T13:49:40.036+07:00  INFO 24405 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-10T13:49:40.038+07:00  INFO 24405 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-10T13:49:40.045+07:00  INFO 24405 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-10T13:49:40.047+07:00  INFO 24405 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-10T13:49:40.077+07:00  INFO 24405 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-10T13:49:40.086+07:00  WARN 24405 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-10T13:49:42.191+07:00  INFO 24405 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-10T13:49:42.192+07:00  INFO 24405 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@48d35d53] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-10T13:49:42.448+07:00  WARN 24405 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-10T13:49:42.448+07:00  WARN 24405 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-10T13:49:42.461+07:00  WARN 24405 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-10T13:49:42.461+07:00  WARN 24405 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-10T13:49:42.473+07:00  WARN 24405 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-10T13:49:42.474+07:00  WARN 24405 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-10T13:49:43.168+07:00  INFO 24405 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-10T13:49:43.177+07:00  INFO 24405 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-10T13:49:43.179+07:00  INFO 24405 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-10T13:49:43.203+07:00  INFO 24405 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-10T13:49:43.209+07:00  WARN 24405 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-10T13:49:43.780+07:00  INFO 24405 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-10T13:49:43.780+07:00  INFO 24405 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@461fcdef] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-10T13:49:43.884+07:00  WARN 24405 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-10T13:49:43.884+07:00  WARN 24405 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-10T13:49:44.283+07:00  INFO 24405 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-10T13:49:44.316+07:00  INFO 24405 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-10T13:49:44.321+07:00  INFO 24405 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-10T13:49:44.321+07:00  INFO 24405 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:49:44.327+07:00  WARN 24405 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-10T13:49:44.481+07:00  INFO 24405 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-10T13:49:44.967+07:00  INFO 24405 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-10T13:49:44.970+07:00  INFO 24405 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-10T13:49:45.006+07:00  INFO 24405 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-10T13:49:45.054+07:00  INFO 24405 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-10T13:49:45.154+07:00  INFO 24405 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-10T13:49:45.185+07:00  INFO 24405 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-10T13:49:45.208+07:00  INFO 24405 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 85404996ms : this is harmless.
2025-09-10T13:49:45.219+07:00  INFO 24405 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-10T13:49:45.222+07:00  INFO 24405 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-10T13:49:45.235+07:00  INFO 24405 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 85404990ms : this is harmless.
2025-09-10T13:49:45.237+07:00  INFO 24405 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-10T13:49:45.251+07:00  INFO 24405 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-10T13:49:45.252+07:00  INFO 24405 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-10T13:49:47.474+07:00  INFO 24405 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-10T13:49:47.475+07:00  INFO 24405 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:49:47.475+07:00  WARN 24405 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-10T13:49:47.788+07:00  INFO 24405 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 10/09/2025@13:45:00+0700 to 10/09/2025@14:00:00+0700
2025-09-10T13:49:47.788+07:00  INFO 24405 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 10/09/2025@13:45:00+0700 to 10/09/2025@14:00:00+0700
2025-09-10T13:49:48.364+07:00  INFO 24405 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-10T13:49:48.364+07:00  INFO 24405 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:49:48.365+07:00  WARN 24405 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-10T13:49:48.652+07:00  INFO 24405 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-10T13:49:48.652+07:00  INFO 24405 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-10T13:49:48.652+07:00  INFO 24405 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-10T13:49:48.652+07:00  INFO 24405 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-10T13:49:48.652+07:00  INFO 24405 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-10T13:49:50.833+07:00  WARN 24405 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 411323a0-814e-45ac-87d1-e6109fbbda3d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-10T13:49:50.839+07:00  INFO 24405 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-10T13:49:51.183+07:00  INFO 24405 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-10T13:49:51.186+07:00  INFO 24405 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-10T13:49:51.186+07:00  INFO 24405 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-10T13:49:51.186+07:00  INFO 24405 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-10T13:49:51.242+07:00  INFO 24405 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-10T13:49:51.242+07:00  INFO 24405 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-10T13:49:51.245+07:00  INFO 24405 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-09-10T13:49:51.261+07:00  INFO 24405 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@543afa19{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-10T13:49:51.263+07:00  INFO 24405 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-10T13:49:51.264+07:00  INFO 24405 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-10T13:49:51.474+07:00  INFO 24405 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-10T13:49:51.474+07:00  INFO 24405 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-10T13:49:51.480+07:00  INFO 24405 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.649 seconds (process running for 16.068)
2025-09-10T13:49:52.106+07:00  INFO 24405 --- [qtp898350050-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-10T13:49:54.565+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0aquiowhng5xcujatf2lrc3xm0
2025-09-10T13:49:54.565+07:00  INFO 24405 --- [qtp898350050-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01cm8akzyvn38b1mbxs1sa5m93q1
2025-09-10T13:49:54.638+07:00  INFO 24405 --- [qtp898350050-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01cm8akzyvn38b1mbxs1sa5m93q1, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:49:54.638+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:49:55.074+07:00  INFO 24405 --- [qtp898350050-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:49:55.087+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:50:01.137+07:00  INFO 24405 --- [qtp898350050-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:50:01.152+07:00  INFO 24405 --- [qtp898350050-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:50:01.153+07:00  INFO 24405 --- [qtp898350050-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:50:01.172+07:00  INFO 24405 --- [qtp898350050-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:50:05.250+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T13:50:05.253+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T13:50:14.220+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:50:14.220+07:00  INFO 24405 --- [qtp898350050-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:50:14.307+07:00  INFO 24405 --- [qtp898350050-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T13:50:14.307+07:00  INFO 24405 --- [qtp898350050-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T13:50:15.287+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:50:15.287+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:50:17.809+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:50:17.815+07:00  INFO 24405 --- [qtp898350050-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:50:24.484+07:00  INFO 24405 --- [qtp898350050-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:50:24.489+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:50:54.430+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-10T13:50:54.475+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T13:51:06.492+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T13:51:14.774+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:51:14.787+07:00  INFO 24405 --- [qtp898350050-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:51:14.793+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:51:14.794+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:51:21.188+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:51:21.198+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:51:21.206+07:00  INFO 24405 --- [qtp898350050-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:51:21.223+07:00  INFO 24405 --- [qtp898350050-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:51:42.389+07:00  INFO 24405 --- [qtp898350050-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:51:42.406+07:00  INFO 24405 --- [qtp898350050-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:51:42.422+07:00  INFO 24405 --- [qtp898350050-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:51:42.439+07:00  INFO 24405 --- [qtp898350050-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:52:04.691+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T13:52:18.454+07:00  INFO 24405 --- [qtp898350050-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:52:18.455+07:00  INFO 24405 --- [qtp898350050-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:52:18.465+07:00  INFO 24405 --- [qtp898350050-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:52:18.465+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:52:53.809+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-10T13:52:53.821+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T13:53:06.838+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T13:53:23.911+07:00  INFO 24405 --- [qtp898350050-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:53:23.928+07:00  INFO 24405 --- [qtp898350050-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:53:23.942+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:53:23.959+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:54:03.937+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T13:54:58.078+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-10T13:54:58.100+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T13:55:06.121+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T13:55:06.123+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T13:56:03.214+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T13:56:12.995+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:56:13.031+07:00  INFO 24405 --- [qtp898350050-66] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T13:56:13.032+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:56:13.036+07:00  INFO 24405 --- [qtp898350050-67] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T13:56:15.127+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:56:15.127+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:56:58.327+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-10T13:56:58.349+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T13:57:06.371+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T13:57:10.860+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:57:10.863+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:57:34.547+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:57:34.547+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:57:34.556+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:57:34.556+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:57:39.418+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:57:39.418+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:57:39.441+07:00  INFO 24405 --- [qtp898350050-35] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T13:57:39.441+07:00  INFO 24405 --- [qtp898350050-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T13:57:40.158+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:57:40.229+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:57:52.447+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:57:52.461+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:57:52.477+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:57:52.484+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:58:02.466+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T13:58:44.906+07:00  INFO 24405 --- [qtp898350050-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:58:44.907+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:58:44.918+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:58:44.918+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:58:47.685+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:58:47.685+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:58:47.698+07:00  INFO 24405 --- [qtp898350050-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T13:58:47.698+07:00  INFO 24405 --- [qtp898350050-66] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T13:58:49.481+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:58:49.484+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:58:57.578+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-10T13:58:57.588+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T13:59:03.195+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:03.196+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:03.204+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:03.205+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:05.601+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T13:59:24.401+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:59:24.401+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:59:40.840+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:40.841+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:40.860+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:40.860+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:41.176+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:41.200+07:00  INFO 24405 --- [qtp898350050-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:41.208+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:41.227+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:47.117+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:47.124+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:47.154+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:47.177+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:47.178+07:00 ERROR 24405 --- [qtp898350050-70] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : 4,
  "companyParentId" : null,
  "companyCode" : "bee",
  "companyLabel" : "Bee Corp",
  "companyFullName" : "BEE LOGISTICS CORPORATION",
  "loginId" : "sandy.vnhph",
  "accountId" : 11174,
  "token" : "070fa2ee4bd6922fa75702d99b7be3ad",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0aquiowhng5xcujatf2lrc3xm0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    }
  },
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:sandy.vnhph"
}, null, {
  "params" : {
    "accountId" : 11174
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-10T13:59:47.178+07:00 ERROR 24405 --- [qtp898350050-66] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : 4,
  "companyParentId" : null,
  "companyCode" : "bee",
  "companyLabel" : "Bee Corp",
  "companyFullName" : "BEE LOGISTICS CORPORATION",
  "loginId" : "sandy.vnhph",
  "accountId" : 11174,
  "token" : "070fa2ee4bd6922fa75702d99b7be3ad",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0aquiowhng5xcujatf2lrc3xm0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    }
  },
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:sandy.vnhph"
}, null, {
  "params" : {
    "accountId" : 11174
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-10T13:59:47.178+07:00 ERROR 24405 --- [qtp898350050-70] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-10T13:59:47.179+07:00 ERROR 24405 --- [qtp898350050-66] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-10T13:59:47.239+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-10T13:59:47.239+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-10T13:59:48.186+07:00  INFO 24405 --- [qtp898350050-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:48.193+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:48.217+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:48.221+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:48.807+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:48.807+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T13:59:48.812+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:48.812+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T13:59:50.998+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:59:50.998+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:59:51.018+07:00  INFO 24405 --- [qtp898350050-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T13:59:51.016+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T13:59:51.993+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T13:59:51.993+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:06.704+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:00:06.706+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 14 PM every day
2025-09-10T14:00:06.706+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-10T14:00:06.707+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-10T14:00:06.709+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 10/09/2025@14:00:06+0700
2025-09-10T14:00:07.086+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 10/09/2025@14:00:00+0700 to 10/09/2025@14:15:00+0700
2025-09-10T14:00:07.086+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 10/09/2025@14:00:00+0700 to 10/09/2025@14:15:00+0700
2025-09-10T14:00:07.086+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:00:17.620+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:00:17.622+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:00:17.632+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:00:17.636+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:00:18.192+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:00:18.194+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:00:18.200+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:00:18.200+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:00:24.958+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:24.958+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:24.973+07:00  INFO 24405 --- [qtp898350050-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:00:24.973+07:00  INFO 24405 --- [qtp898350050-66] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:00:25.663+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:25.663+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:27.835+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:27.835+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:30.947+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:30.947+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:38.653+07:00  INFO 24405 --- [Scheduler-1769472147-1] n.d.m.session.AppHttpSessionListener     : The session node01cm8akzyvn38b1mbxs1sa5m93q1 is destroyed.
2025-09-10T14:00:53.062+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BDService/searchAnnualConferences
2025-09-10T14:00:53.062+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BDService/searchAnnualConferences
2025-09-10T14:00:53.910+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:00:53.911+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:00:53.918+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:00:53.931+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:00:57.599+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:57.599+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:57.604+07:00  INFO 24405 --- [qtp898350050-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:00:57.604+07:00  INFO 24405 --- [qtp898350050-35] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:00:58.229+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 15, expire count 4
2025-09-10T14:00:58.253+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:00:58.349+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:58.349+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:59.788+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:00:59.788+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:01:05.266+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:02:06.362+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:02:28.252+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:02:28.254+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:02:28.271+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:02:28.271+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:02:28.463+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:02:28.479+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:02:28.480+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:02:28.582+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:02:34.208+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:02:34.211+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:02:34.312+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:02:34.314+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:02:34.432+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:02:34.435+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:02:34.445+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:02:34.445+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:02:43.333+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:02:43.333+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:02:43.357+07:00  INFO 24405 --- [qtp898350050-75] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:02:43.357+07:00  INFO 24405 --- [qtp898350050-35] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:02:44.083+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:02:44.083+07:00  INFO 24405 --- [qtp898350050-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:02:57.499+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-10T14:02:57.507+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:03:04.512+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:03:07.187+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:03:07.190+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:03:07.198+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:03:07.200+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:03:07.281+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:03:07.289+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:03:07.340+07:00  INFO 24405 --- [qtp898350050-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:03:07.347+07:00 ERROR 24405 --- [qtp898350050-70] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "sandy.vnhph",
  "accountId" : 11174,
  "token" : "070fa2ee4bd6922fa75702d99b7be3ad",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0aquiowhng5xcujatf2lrc3xm0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    }
  },
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:sandy.vnhph"
}, null, {
  "params" : {
    "accountId" : 11174
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-10T14:03:07.347+07:00 ERROR 24405 --- [qtp898350050-70] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-10T14:03:07.349+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-10T14:03:07.352+07:00  INFO 24405 --- [qtp898350050-78] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:03:13.765+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:03:13.768+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:03:13.794+07:00  INFO 24405 --- [qtp898350050-76] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:03:13.801+07:00  INFO 24405 --- [qtp898350050-76] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:03:18.297+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:03:18.297+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:03:18.310+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:03:18.310+07:00  INFO 24405 --- [qtp898350050-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:03:19.415+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:03:19.415+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:04:06.628+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:04:11.600+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:04:11.600+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:04:11.606+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:04:11.606+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:04:12.189+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:04:12.189+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:04:12.195+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:04:12.195+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:04:15.570+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:04:15.570+07:00  INFO 24405 --- [qtp898350050-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:04:15.588+07:00  INFO 24405 --- [qtp898350050-76] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:04:15.589+07:00  INFO 24405 --- [qtp898350050-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:04:16.638+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:04:16.649+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:04:56.780+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-10T14:04:56.801+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:04:58.819+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:04:58.819+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:04:58.824+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:04:58.824+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:04:59.190+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:04:59.190+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:04:59.195+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:04:59.195+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:05:01.261+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:01.261+07:00  INFO 24405 --- [qtp898350050-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:01.276+07:00  INFO 24405 --- [qtp898350050-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:05:01.276+07:00  INFO 24405 --- [qtp898350050-76] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:05:02.630+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:02.630+07:00  INFO 24405 --- [qtp898350050-77] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:03.809+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:05:03.811+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:05:30.774+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:05:30.775+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:05:30.781+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:05:30.781+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:05:31.188+07:00  INFO 24405 --- [qtp898350050-76] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:05:31.217+07:00  INFO 24405 --- [qtp898350050-76] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:05:31.235+07:00  INFO 24405 --- [qtp898350050-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:05:31.240+07:00  INFO 24405 --- [qtp898350050-77] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:05:39.186+07:00  INFO 24405 --- [qtp898350050-77] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:39.187+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:39.199+07:00  INFO 24405 --- [qtp898350050-77] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:05:39.199+07:00  INFO 24405 --- [qtp898350050-35] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:05:40.455+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:40.455+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:40.777+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:40.777+07:00  INFO 24405 --- [qtp898350050-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:41.452+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:05:41.457+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:06:06.937+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:06:08.219+07:00  INFO 24405 --- [qtp898350050-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:06:08.220+07:00  INFO 24405 --- [qtp898350050-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:06:08.235+07:00  INFO 24405 --- [qtp898350050-77] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:06:08.235+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:06:08.258+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:06:08.258+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:06:08.260+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:06:08.260+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:06:10.469+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:06:10.478+07:00  INFO 24405 --- [qtp898350050-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:06:10.516+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:06:10.521+07:00  INFO 24405 --- [qtp898350050-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:06:11.737+07:00  INFO 24405 --- [qtp898350050-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:06:11.737+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:06:56.039+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-10T14:06:56.049+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:07:03.064+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:07:22.934+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:07:22.935+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:07:22.943+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:07:22.943+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:07:23.184+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:07:23.184+07:00  INFO 24405 --- [qtp898350050-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:07:23.190+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:07:23.191+07:00  INFO 24405 --- [qtp898350050-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:07:25.198+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:07:25.203+07:00  INFO 24405 --- [qtp898350050-101] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:07:25.212+07:00  INFO 24405 --- [qtp898350050-101] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:07:25.212+07:00  INFO 24405 --- [qtp898350050-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:07:26.342+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:07:26.342+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:08:01.610+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:08:01.611+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:08:01.616+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:08:01.616+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:08:02.220+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:08:02.238+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:08:02.263+07:00  INFO 24405 --- [qtp898350050-101] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:08:02.267+07:00  INFO 24405 --- [qtp898350050-101] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:08:06.192+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:08:06.826+07:00  INFO 24405 --- [qtp898350050-78] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:08:06.847+07:00  INFO 24405 --- [qtp898350050-78] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:08:06.860+07:00  INFO 24405 --- [qtp898350050-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:08:06.865+07:00  INFO 24405 --- [qtp898350050-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:08:07.843+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:08:07.843+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:08:25.167+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:08:25.168+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:08:25.174+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:08:25.174+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:08:25.183+07:00  INFO 24405 --- [qtp898350050-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:08:25.183+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:08:25.188+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:08:25.258+07:00  INFO 24405 --- [qtp898350050-78] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:08:32.570+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:08:32.570+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:08:32.585+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:08:32.585+07:00  INFO 24405 --- [qtp898350050-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:08:34.052+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:08:34.052+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:08:55.343+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 3
2025-09-10T14:08:55.376+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:09:02.389+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:09:10.220+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:09:10.229+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:09:10.260+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:09:10.261+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:09:10.624+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:09:10.626+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:09:10.704+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:09:10.713+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:09:12.589+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:09:12.589+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:09:12.596+07:00  INFO 24405 --- [qtp898350050-99] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:09:12.596+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:09:13.596+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:09:13.601+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:09:48.883+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:09:48.905+07:00  INFO 24405 --- [qtp898350050-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:09:48.915+07:00  INFO 24405 --- [qtp898350050-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:09:48.915+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:09:49.194+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:09:49.195+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:09:49.231+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:09:49.231+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:09:51.325+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:09:51.325+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:09:51.445+07:00  INFO 24405 --- [qtp898350050-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:09:51.445+07:00  INFO 24405 --- [qtp898350050-75] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:09:52.101+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:09:52.101+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:10:05.488+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:10:05.492+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:10:13.180+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:10:13.181+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:10:13.183+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:10:13.186+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:10:13.195+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:10:13.195+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:10:13.195+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:10:13.219+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:10:17.558+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:10:17.558+07:00  INFO 24405 --- [qtp898350050-76] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:10:17.563+07:00  INFO 24405 --- [qtp898350050-76] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:10:17.563+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:10:18.202+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:10:18.203+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:10:18.210+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:10:18.213+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:10:32.347+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:10:32.347+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:10:32.400+07:00  INFO 24405 --- [qtp898350050-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:10:32.399+07:00  INFO 24405 --- [qtp898350050-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:10:33.557+07:00  INFO 24405 --- [qtp898350050-100] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:10:33.557+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:10:54.611+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-10T14:10:54.619+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:11:06.632+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:11:16.088+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:11:16.094+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:11:16.110+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:11:16.115+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:11:16.181+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:11:16.182+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:11:16.185+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:11:16.186+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:11:23.110+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:11:23.110+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:11:23.135+07:00  INFO 24405 --- [qtp898350050-99] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:11:23.135+07:00  INFO 24405 --- [qtp898350050-71] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:11:23.816+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:11:23.816+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:11:25.124+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:11:25.124+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:11:59.201+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:11:59.204+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:11:59.260+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:11:59.260+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:11:59.266+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:11:59.286+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:11:59.304+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:11:59.374+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:12:04.735+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:12:05.198+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:12:05.198+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:12:05.198+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:12:05.199+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:12:05.218+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:12:05.218+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:12:05.218+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:12:05.219+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:12:08.514+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:12:08.514+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:12:08.527+07:00  INFO 24405 --- [qtp898350050-67] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:12:08.527+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:12:09.677+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:12:09.677+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:12:53.842+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 1
2025-09-10T14:12:53.875+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:13:04.239+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:04.255+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:04.288+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:04.288+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:04.401+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:04.410+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:04.493+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:04.497+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:06.898+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:13:12.254+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:12.255+07:00  INFO 24405 --- [qtp898350050-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:12.256+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:12.257+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:12.268+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:12.269+07:00  INFO 24405 --- [qtp898350050-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:12.270+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:12.321+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:19.188+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:19.189+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:19.202+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:19.202+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:19.263+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:19.264+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:19.266+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:19.267+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:39.351+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:39.354+07:00  INFO 24405 --- [qtp898350050-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:39.359+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:39.361+07:00  INFO 24405 --- [qtp898350050-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:39.512+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:39.512+07:00  INFO 24405 --- [qtp898350050-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:13:39.514+07:00  INFO 24405 --- [qtp898350050-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:13:39.514+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:14:03.994+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:14:04.128+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:14:04.128+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:14:04.157+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:14:04.157+07:00  INFO 24405 --- [qtp898350050-75] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:14:04.949+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:14:04.949+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:14:58.136+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 2
2025-09-10T14:14:58.149+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:15:06.165+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:15:06.166+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:15:06.168+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 10/09/2025@14:15:06+0700
2025-09-10T14:15:06.196+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 10/09/2025@14:15:00+0700 to 10/09/2025@14:30:00+0700
2025-09-10T14:15:06.196+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 10/09/2025@14:15:00+0700 to 10/09/2025@14:30:00+0700
2025-09-10T14:15:06.197+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-10T14:16:03.312+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:16:25.201+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:25.210+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:25.210+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:25.232+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:26.191+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:26.193+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:26.199+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:26.205+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:31.201+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:31.202+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:31.202+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:31.207+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:31.214+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:31.214+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:31.214+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:31.227+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:42.207+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:42.208+07:00  INFO 24405 --- [qtp898350050-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:42.217+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:42.217+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:16:42.227+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:42.227+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:42.241+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:42.253+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:16:46.774+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:16:46.774+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:16:46.783+07:00  INFO 24405 --- [qtp898350050-109] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:16:46.783+07:00  INFO 24405 --- [qtp898350050-99] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:16:48.532+07:00  INFO 24405 --- [qtp898350050-100] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:16:48.532+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:16:48.952+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:16:48.952+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:16:57.504+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-09-10T14:16:57.526+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:17:01.595+07:00  INFO 24405 --- [qtp898350050-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:17:01.597+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:17:06.542+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:18:02.640+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:18:57.748+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-10T14:18:57.757+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:19:05.769+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:19:09.104+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:19:09.106+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:19:42.129+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:19:42.150+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:19:42.161+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:19:42.167+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:19:44.914+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:19:44.914+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:19:44.962+07:00  INFO 24405 --- [qtp898350050-109] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:19:44.962+07:00  INFO 24405 --- [qtp898350050-110] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:19:45.859+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:19:45.859+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:19:46.710+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:19:46.710+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:19:47.015+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:19:47.015+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:20:06.872+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:20:06.873+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:20:57.976+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 0
2025-09-10T14:20:57.991+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:21:05.005+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:21:09.203+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:21:09.212+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:21:09.340+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:21:09.360+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:21:32.219+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:21:32.222+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:21:32.224+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:21:32.234+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:21:33.228+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:21:33.247+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:21:33.249+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:21:33.276+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:21:41.575+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:21:41.578+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:21:41.590+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:21:41.590+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:21:42.203+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:21:42.204+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:21:42.210+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:21:42.210+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:06.103+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:22:38.179+07:00  INFO 24405 --- [qtp898350050-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:38.179+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:38.184+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:38.184+07:00  INFO 24405 --- [qtp898350050-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:38.234+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:38.234+07:00  INFO 24405 --- [qtp898350050-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:38.237+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:38.237+07:00  INFO 24405 --- [qtp898350050-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:47.057+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:47.058+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:47.064+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:47.064+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:47.174+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:47.174+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:47.176+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:47.176+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:54.944+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:54.945+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:54.948+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:54.948+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:55.175+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:55.176+07:00  INFO 24405 --- [qtp898350050-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:22:55.179+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:55.185+07:00  INFO 24405 --- [qtp898350050-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:22:57.176+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-10T14:22:57.184+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:23:01.079+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:01.081+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:01.086+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:01.092+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:01.175+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:01.175+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:01.178+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:01.178+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:03.190+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:23:03.190+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:23:03.254+07:00  INFO 24405 --- [qtp898350050-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:23:03.274+07:00  INFO 24405 --- [qtp898350050-109] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:23:03.893+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:23:03.893+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:23:04.197+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:23:27.386+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:27.387+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:27.393+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:27.393+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:28.220+07:00  INFO 24405 --- [qtp898350050-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:28.221+07:00  INFO 24405 --- [qtp898350050-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:28.234+07:00  INFO 24405 --- [qtp898350050-105] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:28.235+07:00  INFO 24405 --- [qtp898350050-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:42.339+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:42.341+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:42.348+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:42.349+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:43.182+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:43.183+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:43.187+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:43.189+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:49.182+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:49.183+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:49.187+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:49.191+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:49.235+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:49.238+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:23:49.238+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:49.242+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:23:51.727+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:23:51.727+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:23:51.927+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:23:51.927+07:00  INFO 24405 --- [qtp898350050-75] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:23:52.646+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:23:52.646+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:23:53.668+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:23:53.668+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:24:06.299+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:24:54.551+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:24:54.553+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:24:54.564+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:24:54.564+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:24:55.180+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:24:55.180+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:24:55.185+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:24:55.186+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:24:56.414+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 1
2025-09-10T14:24:56.424+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:25:03.439+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:25:03.442+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:25:31.776+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:25:31.778+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:25:31.785+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:25:31.785+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:25:32.181+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:25:32.190+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:25:32.279+07:00  INFO 24405 --- [qtp898350050-104] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:25:32.285+07:00  INFO 24405 --- [qtp898350050-104] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:05.726+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:05.726+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:05.733+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:05.733+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:06.177+07:00  INFO 24405 --- [qtp898350050-104] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:06.178+07:00  INFO 24405 --- [qtp898350050-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:06.182+07:00  INFO 24405 --- [qtp898350050-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:06.182+07:00  INFO 24405 --- [qtp898350050-104] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:06.546+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:26:19.775+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:19.776+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:19.780+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:19.780+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:20.179+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:20.180+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:20.183+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:20.186+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:22.375+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:26:22.375+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:26:22.579+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:26:22.579+07:00  INFO 24405 --- [qtp898350050-109] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:26:23.030+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:26:23.031+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:26:23.289+07:00  INFO 24405 --- [qtp898350050-104] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:26:23.290+07:00  INFO 24405 --- [qtp898350050-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:26:41.941+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:41.960+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:41.977+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:41.978+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:42.177+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:42.177+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:26:42.180+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:42.180+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:26:44.819+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:26:44.819+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:26:44.836+07:00  INFO 24405 --- [qtp898350050-109] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:26:44.836+07:00  INFO 24405 --- [qtp898350050-110] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:26:45.682+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:26:45.682+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:26:55.631+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:26:55.643+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:27:00.742+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:00.743+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:00.747+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:00.747+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:01.176+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:01.177+07:00  INFO 24405 --- [qtp898350050-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:01.179+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:01.180+07:00  INFO 24405 --- [qtp898350050-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:02.657+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:27:03.369+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:03.369+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:03.390+07:00  INFO 24405 --- [qtp898350050-35] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:27:03.394+07:00  INFO 24405 --- [qtp898350050-110] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:27:04.030+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:04.030+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:12.219+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:12.220+07:00  INFO 24405 --- [qtp898350050-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:12.230+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:12.230+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:13.184+07:00  INFO 24405 --- [qtp898350050-104] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:13.186+07:00  INFO 24405 --- [qtp898350050-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:13.188+07:00  INFO 24405 --- [qtp898350050-104] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:13.194+07:00  INFO 24405 --- [qtp898350050-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:14.618+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:14.618+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:14.646+07:00  INFO 24405 --- [qtp898350050-110] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:27:14.646+07:00  INFO 24405 --- [qtp898350050-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:27:15.654+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:15.654+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:25.855+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:25.856+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:25.862+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:25.862+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:26.175+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:26.176+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:27:26.179+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:26.182+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:27:29.668+07:00  INFO 24405 --- [qtp898350050-104] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:29.668+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:29.677+07:00  INFO 24405 --- [qtp898350050-104] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:27:29.677+07:00  INFO 24405 --- [qtp898350050-106] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:27:30.324+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:30.325+07:00  INFO 24405 --- [qtp898350050-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:30.589+07:00  INFO 24405 --- [qtp898350050-104] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:27:30.589+07:00  INFO 24405 --- [qtp898350050-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:28:05.766+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:28:54.894+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-09-10T14:28:54.907+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:29:04.201+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:29:04.203+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:29:04.217+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:29:04.217+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:29:06.927+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:29:09.267+07:00  INFO 24405 --- [qtp898350050-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:29:09.270+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:29:09.289+07:00  INFO 24405 --- [qtp898350050-105] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:29:09.328+07:00  INFO 24405 --- [qtp898350050-106] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:29:10.079+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:29:10.092+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:29:25.646+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:29:25.647+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:29:33.453+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:29:33.454+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:29:47.225+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:29:47.226+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:29:47.286+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:29:47.286+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:29:59.472+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:29:59.480+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:29:59.487+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:29:59.487+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:30:04.522+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:30:04.528+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:30:04.537+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:30:04.540+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:30:05.024+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-10T14:30:05.027+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 10/09/2025@14:30:05+0700
2025-09-10T14:30:05.044+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 10/09/2025@14:30:00+0700 to 10/09/2025@14:45:00+0700
2025-09-10T14:30:05.044+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 10/09/2025@14:30:00+0700 to 10/09/2025@14:45:00+0700
2025-09-10T14:30:05.044+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:30:05.044+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:30:21.909+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:30:21.909+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:30:22.339+07:00  INFO 24405 --- [qtp898350050-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:30:22.339+07:00  INFO 24405 --- [qtp898350050-103] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:30:23.858+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:30:23.858+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:30:37.503+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:30:37.515+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:30:37.528+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:30:37.540+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:30:41.469+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:30:41.470+07:00  INFO 24405 --- [qtp898350050-76] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:30:41.480+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:30:41.480+07:00  INFO 24405 --- [qtp898350050-76] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:30:50.409+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:30:50.409+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:30:50.430+07:00  INFO 24405 --- [qtp898350050-174] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:30:50.431+07:00  INFO 24405 --- [qtp898350050-110] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:30:51.161+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:30:51.161+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:30:54.147+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-10T14:30:54.153+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:31:06.168+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:32:04.279+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:32:44.006+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:32:44.012+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:32:44.046+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:32:44.060+07:00 ERROR 24405 --- [qtp898350050-106] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "sandy.vnhph",
  "accountId" : 11174,
  "token" : "070fa2ee4bd6922fa75702d99b7be3ad",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0aquiowhng5xcujatf2lrc3xm0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:sandy.vnhph"
}, null, {
  "params" : {
    "accountId" : 11174
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-10T14:32:44.061+07:00 ERROR 24405 --- [qtp898350050-103] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "sandy.vnhph",
  "accountId" : 11174,
  "token" : "070fa2ee4bd6922fa75702d99b7be3ad",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0aquiowhng5xcujatf2lrc3xm0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:sandy.vnhph"
}, null, {
  "params" : {
    "accountId" : 11174
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-10T14:32:44.061+07:00 ERROR 24405 --- [qtp898350050-103] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-10T14:32:44.061+07:00 ERROR 24405 --- [qtp898350050-106] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 93 common frames omitted

2025-09-10T14:32:44.065+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-10T14:32:44.065+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-10T14:32:44.125+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:32:58.411+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 1
2025-09-10T14:32:58.449+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:33:02.621+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:33:02.623+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:33:02.630+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:33:02.630+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:33:05.107+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:33:05.107+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:33:05.115+07:00  INFO 24405 --- [qtp898350050-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:33:05.115+07:00  INFO 24405 --- [qtp898350050-110] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:33:05.820+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:33:05.822+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:33:06.398+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:33:06.398+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:33:06.470+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:33:28.461+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:33:28.462+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:33:28.470+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:33:28.470+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:33:36.088+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:33:36.088+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:33:36.121+07:00  INFO 24405 --- [qtp898350050-110] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:33:36.121+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:33:36.802+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:33:36.802+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:33:50.661+07:00  INFO 24405 --- [qtp898350050-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:33:50.663+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:33:50.669+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:33:50.669+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:34:03.555+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:34:08.090+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:34:08.090+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:34:08.098+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:34:08.098+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:34:10.190+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:10.192+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:10.202+07:00  INFO 24405 --- [qtp898350050-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:34:10.229+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:34:10.980+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:10.980+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:12.315+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:12.315+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:40.166+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:34:40.167+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:34:40.176+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:34:40.176+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:34:53.592+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:53.592+07:00  INFO 24405 --- [qtp898350050-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:53.614+07:00  INFO 24405 --- [qtp898350050-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:34:53.615+07:00  INFO 24405 --- [qtp898350050-103] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:34:54.664+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:54.664+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:57.664+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-10T14:34:57.673+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:34:58.237+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:34:58.237+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:35:06.686+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:35:06.688+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:36:02.781+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:36:34.352+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:36:34.352+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:36:34.355+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:36:34.355+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:36:35.191+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:36:35.192+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:36:35.196+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:36:35.196+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:36:57.903+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-10T14:36:57.918+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:37:05.937+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:38:02.024+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:38:33.458+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:38:33.458+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:38:33.467+07:00  INFO 24405 --- [qtp898350050-110] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:38:33.467+07:00  INFO 24405 --- [qtp898350050-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:38:34.117+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:38:34.117+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:38:58.124+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-10T14:38:58.135+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:39:05.149+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:40:06.235+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:40:06.235+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:40:46.234+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:40:46.282+07:00  INFO 24405 --- [qtp898350050-172] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:40:46.311+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:40:46.312+07:00  INFO 24405 --- [qtp898350050-172] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:40:47.192+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:40:47.192+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:40:47.198+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:40:47.200+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:40:49.537+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:40:49.538+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:40:49.541+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:40:49.546+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:40:50.182+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:40:50.183+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:40:50.187+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:40:50.196+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:40:57.324+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 2
2025-09-10T14:40:57.331+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:41:01.817+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:01.827+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:01.859+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:01.882+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:02.178+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:02.178+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:02.189+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:02.190+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:04.346+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:41:08.872+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:08.872+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:08.881+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:08.883+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:09.177+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:09.179+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:09.195+07:00  INFO 24405 --- [qtp898350050-173] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:09.197+07:00  INFO 24405 --- [qtp898350050-173] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:11.805+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:11.806+07:00  INFO 24405 --- [qtp898350050-173] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:11.842+07:00  INFO 24405 --- [qtp898350050-173] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:11.838+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:12.212+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:12.220+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:12.223+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:12.232+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:15.168+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:15.175+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:15.181+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:15.181+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:16.189+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:16.190+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:16.195+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:16.195+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:23.057+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:23.058+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:23.067+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:23.067+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:23.178+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:23.181+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:23.183+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:23.183+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:28.375+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:28.377+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:28.390+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:28.390+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:29.190+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:29.193+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:29.200+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:29.200+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:34.023+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:34.025+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:34.029+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:34.030+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:35.187+07:00  INFO 24405 --- [qtp898350050-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:35.189+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:35.200+07:00  INFO 24405 --- [qtp898350050-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:35.200+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:39.089+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:39.089+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:39.097+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:39.097+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:40.179+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:40.180+07:00  INFO 24405 --- [qtp898350050-173] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:40.183+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:40.183+07:00  INFO 24405 --- [qtp898350050-173] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:55.961+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:55.969+07:00  INFO 24405 --- [qtp898350050-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:56.003+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:56.003+07:00  INFO 24405 --- [qtp898350050-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:57.198+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:57.198+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:41:57.201+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:41:57.201+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:42:06.458+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:42:56.604+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 3
2025-09-10T14:42:56.620+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:43:03.633+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:44:06.728+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:44:52.409+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:44:52.409+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:44:52.628+07:00  INFO 24405 --- [qtp898350050-174] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:44:52.628+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:44:53.806+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:44:53.806+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:44:55.808+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-10T14:44:55.816+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:45:02.826+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:45:02.827+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:45:02.828+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-10T14:45:02.829+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 10/09/2025@14:45:02+0700
2025-09-10T14:45:02.869+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 10/09/2025@14:45:00+0700 to 10/09/2025@15:00:00+0700
2025-09-10T14:45:02.869+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 10/09/2025@14:45:00+0700 to 10/09/2025@15:00:00+0700
2025-09-10T14:45:14.547+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:45:14.547+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:46:05.971+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:46:15.808+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:15.810+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:15.819+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:15.819+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:16.186+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:16.196+07:00  INFO 24405 --- [qtp898350050-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:16.216+07:00  INFO 24405 --- [qtp898350050-75] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:16.217+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:26.898+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:26.909+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:26.940+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:26.946+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:27.193+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:27.198+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:27.236+07:00  INFO 24405 --- [qtp898350050-240] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:27.262+07:00 ERROR 24405 --- [qtp898350050-103] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "sandy.vnhph",
  "accountId" : 11174,
  "token" : "070fa2ee4bd6922fa75702d99b7be3ad",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0aquiowhng5xcujatf2lrc3xm0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    }
  },
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:sandy.vnhph"
}, null, {
  "params" : {
    "accountId" : 11174
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-10T14:46:27.262+07:00 ERROR 24405 --- [qtp898350050-109] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "sandy.vnhph",
  "accountId" : 11174,
  "token" : "070fa2ee4bd6922fa75702d99b7be3ad",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0aquiowhng5xcujatf2lrc3xm0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    }
  },
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:sandy.vnhph"
}, null, {
  "params" : {
    "accountId" : 11174
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-10T14:46:27.262+07:00 ERROR 24405 --- [qtp898350050-109] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-10T14:46:27.262+07:00 ERROR 24405 --- [qtp898350050-103] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-10T14:46:27.265+07:00  INFO 24405 --- [qtp898350050-240] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:27.269+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-10T14:46:27.268+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-10T14:46:38.811+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:38.812+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:38.819+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:38.819+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:39.180+07:00  INFO 24405 --- [qtp898350050-240] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:39.180+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:39.184+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:39.184+07:00  INFO 24405 --- [qtp898350050-240] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:51.848+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:51.851+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:51.861+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:51.861+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:52.186+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:52.187+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:52.192+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:52.192+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:55.078+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 1
2025-09-10T14:46:55.087+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:46:57.847+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:57.850+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:57.853+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:57.853+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:58.183+07:00  INFO 24405 --- [qtp898350050-240] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:58.183+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:46:58.188+07:00  INFO 24405 --- [qtp898350050-240] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:46:58.188+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:02.099+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:47:02.258+07:00  INFO 24405 --- [qtp898350050-240] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:47:02.258+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:47:02.268+07:00  INFO 24405 --- [qtp898350050-240] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:47:02.268+07:00  INFO 24405 --- [qtp898350050-103] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:47:03.049+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:47:03.049+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:47:05.481+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:47:05.482+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:47:26.510+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:26.511+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:26.516+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:26.516+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:27.187+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:27.187+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:27.191+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:27.191+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:36.196+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:36.197+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:36.198+07:00  INFO 24405 --- [qtp898350050-240] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:36.198+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:36.205+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:36.205+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:36.205+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:36.205+07:00  INFO 24405 --- [qtp898350050-240] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:45.649+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:45.650+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:45.657+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:45.657+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:46.181+07:00  INFO 24405 --- [qtp898350050-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:46.181+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:46.183+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:46.184+07:00  INFO 24405 --- [qtp898350050-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:48.423+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:48.424+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:48.429+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:48.429+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:49.182+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:49.183+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:49.185+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:49.185+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:53.076+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:53.078+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:47:53.085+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:53.085+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:47:55.228+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:47:55.228+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:47:55.251+07:00  INFO 24405 --- [qtp898350050-103] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:47:55.251+07:00  INFO 24405 --- [qtp898350050-109] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:47:56.145+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:47:56.145+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:48:01.940+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:48:01.941+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:48:05.203+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:48:33.063+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:48:33.070+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:48:33.078+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:48:33.078+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:48:43.293+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:48:43.294+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:48:43.308+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:48:43.308+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:48:46.956+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:48:46.956+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:48:46.970+07:00  INFO 24405 --- [qtp898350050-230] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:48:46.988+07:00  INFO 24405 --- [qtp898350050-238] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:48:47.683+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:48:47.684+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:48:51.069+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:48:51.070+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:48:54.346+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-10T14:48:54.360+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:49:06.447+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:49:07.637+07:00  INFO 24405 --- [qtp898350050-240] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:49:07.637+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:49:07.665+07:00  INFO 24405 --- [qtp898350050-240] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:49:07.665+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:49:10.188+07:00  INFO 24405 --- [qtp898350050-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:49:10.190+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:49:10.195+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:49:10.199+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:49:12.999+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:49:12.999+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:49:13.108+07:00  INFO 24405 --- [qtp898350050-109] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:49:13.180+07:00  INFO 24405 --- [qtp898350050-238] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:49:13.647+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:49:13.647+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:49:16.233+07:00  INFO 24405 --- [qtp898350050-110] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:49:16.233+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:49:45.712+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:49:45.713+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:49:45.717+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:49:45.725+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:49:48.037+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:49:48.040+07:00  INFO 24405 --- [qtp898350050-240] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:49:48.075+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:49:48.075+07:00  INFO 24405 --- [qtp898350050-240] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:49:48.693+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:49:48.693+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:50:04.555+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:50:04.558+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:50:53.675+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-10T14:50:53.684+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:51:06.704+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:52:03.797+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:52:18.204+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:52:18.207+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:52:18.333+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:52:18.356+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:52:26.859+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:26.860+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:26.872+07:00  INFO 24405 --- [qtp898350050-238] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:52:26.874+07:00  INFO 24405 --- [qtp898350050-103] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:52:27.540+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:27.540+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:29.248+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:29.248+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:33.215+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:33.215+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:49.292+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:52:49.292+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:52:49.300+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:52:49.300+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:52:52.559+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:52.559+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:52.563+07:00  INFO 24405 --- [qtp898350050-103] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:52:52.563+07:00  INFO 24405 --- [qtp898350050-239] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:52:53.107+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:53.107+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:52:56.190+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:52:56.202+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:52:56.237+07:00  INFO 24405 --- [qtp898350050-239] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:52:56.242+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:52:57.920+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 1
2025-09-10T14:52:57.931+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:53:06.955+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:53:26.439+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:53:26.447+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:53:26.571+07:00  INFO 24405 --- [qtp898350050-241] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:53:26.599+07:00  INFO 24405 --- [qtp898350050-241] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:53:27.191+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:53:27.191+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:53:27.197+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:53:27.202+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:53:31.340+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:53:31.340+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:53:31.431+07:00  INFO 24405 --- [qtp898350050-174] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:53:31.461+07:00  INFO 24405 --- [qtp898350050-230] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:53:32.166+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:53:32.166+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:53:57.096+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:53:57.097+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:53:57.100+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:53:57.101+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:53:57.218+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:53:57.218+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:53:57.221+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:53:57.221+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:03.030+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:54:08.084+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:08.087+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:08.211+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:08.211+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:09.195+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:09.195+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:09.200+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:09.200+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:18.123+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:18.130+07:00  INFO 24405 --- [qtp898350050-239] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:18.136+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:18.138+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:19.189+07:00  INFO 24405 --- [qtp898350050-239] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:19.191+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:19.197+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:19.204+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:22.016+07:00  INFO 24405 --- [qtp898350050-172] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:22.016+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:22.020+07:00  INFO 24405 --- [qtp898350050-172] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:22.020+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:22.274+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:22.284+07:00  INFO 24405 --- [qtp898350050-241] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:54:22.335+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:22.404+07:00  INFO 24405 --- [qtp898350050-241] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:54:27.671+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:54:27.671+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:54:27.853+07:00  INFO 24405 --- [qtp898350050-239] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:54:27.853+07:00  INFO 24405 --- [qtp898350050-103] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:54:28.288+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:54:28.288+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:54:33.473+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:54:33.473+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:54:58.155+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 1
2025-09-10T14:54:58.169+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:55:06.183+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:55:06.184+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T14:55:36.133+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:55:36.134+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:55:36.139+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:55:36.139+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:55:54.181+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:55:54.182+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:55:54.189+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:55:54.189+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:56:02.267+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:56:09.150+07:00  INFO 24405 --- [qtp898350050-172] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:56:09.152+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:56:09.158+07:00  INFO 24405 --- [qtp898350050-172] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:56:09.158+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:56:14.017+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:56:14.017+07:00  INFO 24405 --- [qtp898350050-172] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:56:14.203+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:56:14.203+07:00  INFO 24405 --- [qtp898350050-172] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:56:14.636+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:56:14.636+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:56:20.023+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:56:20.023+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:56:58.372+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 4
2025-09-10T14:56:58.379+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T14:56:59.080+07:00  INFO 24405 --- [qtp898350050-172] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:56:59.088+07:00  INFO 24405 --- [qtp898350050-172] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:56:59.107+07:00  INFO 24405 --- [qtp898350050-243] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:56:59.108+07:00  INFO 24405 --- [qtp898350050-243] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:57:05.391+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:57:10.185+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:57:10.185+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:57:10.193+07:00  INFO 24405 --- [qtp898350050-103] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:57:10.193+07:00  INFO 24405 --- [qtp898350050-174] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:57:11.118+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:57:11.118+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:57:15.036+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:57:15.036+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:57:55.995+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:57:56.003+07:00  INFO 24405 --- [qtp898350050-172] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:57:56.018+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:57:56.018+07:00  INFO 24405 --- [qtp898350050-172] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:57:59.724+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:57:59.727+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:57:59.744+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:57:59.745+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:58:02.494+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:58:02.494+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:58:02.506+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:58:02.506+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:58:04.200+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:58:04.200+07:00  INFO 24405 --- [qtp898350050-241] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:58:04.249+07:00  INFO 24405 --- [qtp898350050-241] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:58:04.249+07:00  INFO 24405 --- [qtp898350050-174] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:58:05.007+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:58:05.007+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:58:06.540+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:58:19.098+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:58:19.098+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:58:23.265+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:58:23.266+07:00  INFO 24405 --- [qtp898350050-241] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:58:55.429+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:58:55.431+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:58:55.438+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:58:55.438+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:58:57.634+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 3
2025-09-10T14:58:57.646+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T14:59:04.659+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T14:59:36.694+07:00  INFO 24405 --- [qtp898350050-241] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:59:36.695+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T14:59:36.735+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:59:36.734+07:00  INFO 24405 --- [qtp898350050-241] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T14:59:44.179+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:59:44.179+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:59:44.222+07:00  INFO 24405 --- [qtp898350050-106] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:59:44.222+07:00  INFO 24405 --- [qtp898350050-238] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T14:59:45.075+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T14:59:45.075+07:00  INFO 24405 --- [qtp898350050-243] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:00:06.881+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-10T15:00:06.897+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 15 PM every day
2025-09-10T15:00:06.897+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T15:00:06.899+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-10T15:00:06.900+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 10/09/2025@15:00:06+0700
2025-09-10T15:00:06.991+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 10/09/2025@15:00:00+0700 to 10/09/2025@15:15:00+0700
2025-09-10T15:00:06.991+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 10/09/2025@15:00:00+0700 to 10/09/2025@15:15:00+0700
2025-09-10T15:00:06.992+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-10T15:00:06.992+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:00:12.177+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:00:12.182+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:00:12.194+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:00:12.195+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:00:14.693+07:00  INFO 24405 --- [qtp898350050-243] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:00:14.694+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:00:14.703+07:00  INFO 24405 --- [qtp898350050-243] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:00:14.709+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:00:22.507+07:00  INFO 24405 --- [qtp898350050-239] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:00:22.509+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:00:22.535+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:00:22.537+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:00:25.088+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:00:25.088+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:00:25.091+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:00:25.091+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:00:29.246+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:00:29.257+07:00  INFO 24405 --- [qtp898350050-241] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:00:29.439+07:00  INFO 24405 --- [qtp898350050-239] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T15:00:29.439+07:00  INFO 24405 --- [qtp898350050-241] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T15:00:29.876+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:00:29.876+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:00:34.916+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:00:34.916+07:00  INFO 24405 --- [qtp898350050-172] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:00:57.131+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 0
2025-09-10T15:00:57.194+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T15:01:04.205+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:01:28.031+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:01:28.034+07:00  INFO 24405 --- [qtp898350050-239] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:01:28.046+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:01:28.052+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:01:44.199+07:00  INFO 24405 --- [qtp898350050-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:01:44.204+07:00  INFO 24405 --- [qtp898350050-243] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:01:44.225+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:01:44.230+07:00  INFO 24405 --- [qtp898350050-243] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:01:50.251+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:01:50.252+07:00  INFO 24405 --- [qtp898350050-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:01:50.261+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:01:50.262+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:01:55.164+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:01:55.164+07:00  INFO 24405 --- [qtp898350050-243] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:01:55.356+07:00  INFO 24405 --- [qtp898350050-239] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T15:01:55.356+07:00  INFO 24405 --- [qtp898350050-243] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T15:01:55.780+07:00  INFO 24405 --- [qtp898350050-172] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:01:55.780+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:06.346+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:02:09.691+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:09.692+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:10.684+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:10.684+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:30.932+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:30.932+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:35.290+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:35.290+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:38.427+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:38.427+07:00  INFO 24405 --- [qtp898350050-241] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:02:53.759+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:02:53.765+07:00  INFO 24405 --- [qtp898350050-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:02:53.771+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:02:53.793+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:02:56.441+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-10T15:02:56.450+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:02:56.483+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:02:56.483+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:02:56.490+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:02:56.493+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:03:01.993+07:00  INFO 24405 --- [qtp898350050-243] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:03:01.996+07:00  INFO 24405 --- [qtp898350050-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:03:02.005+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:03:02.005+07:00  INFO 24405 --- [qtp898350050-243] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:03:03.457+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:03:04.379+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:03:04.384+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:03:04.408+07:00  INFO 24405 --- [qtp898350050-174] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T15:03:04.424+07:00  INFO 24405 --- [qtp898350050-109] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T15:03:05.244+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:03:05.244+07:00  INFO 24405 --- [qtp898350050-109] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:03:50.868+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:03:50.869+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:03:50.880+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:03:50.901+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:03:57.048+07:00  INFO 24405 --- [qtp898350050-230] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:03:57.049+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:03:57.058+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:03:57.058+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:04:06.553+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:04:07.756+07:00  INFO 24405 --- [qtp898350050-239] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:04:07.757+07:00  INFO 24405 --- [qtp898350050-174] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:04:07.772+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:04:07.772+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:04:35.096+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:04:35.097+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:04:35.276+07:00  INFO 24405 --- [qtp898350050-174] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T15:04:35.276+07:00  INFO 24405 --- [qtp898350050-230] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T15:04:35.651+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:04:35.651+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:04:43.162+07:00  INFO 24405 --- [qtp898350050-230] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:04:43.163+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:04:55.671+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-10T15:04:55.696+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T15:05:02.710+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T15:05:02.712+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:06:05.826+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:06:54.920+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-10T15:06:54.928+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:07:06.941+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:07:19.106+07:00  INFO 24405 --- [qtp898350050-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:07:19.106+07:00  INFO 24405 --- [qtp898350050-239] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:07:19.119+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:07:19.119+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:08:05.035+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:08:54.122+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-10T15:08:54.131+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:09:06.152+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:09:58.412+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:09:58.412+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:09:58.453+07:00  INFO 24405 --- [qtp898350050-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T15:09:58.453+07:00  INFO 24405 --- [qtp898350050-174] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-10T15:09:59.961+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:09:59.961+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:10:04.261+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T15:10:04.262+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:10:07.615+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:10:07.615+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:10:17.248+07:00  INFO 24405 --- [qtp898350050-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:10:17.248+07:00  INFO 24405 --- [qtp898350050-103] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:10:58.378+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-09-10T15:10:58.413+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T15:11:06.427+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:12:03.536+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:12:57.618+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-10T15:12:57.624+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:13:06.645+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:13:39.384+07:00  INFO 24405 --- [qtp898350050-280] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:13:39.384+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:13:40.521+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:13:40.521+07:00  INFO 24405 --- [qtp898350050-240] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:13:41.837+07:00  INFO 24405 --- [qtp898350050-240] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:13:41.872+07:00  INFO 24405 --- [qtp898350050-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:13:44.414+07:00  INFO 24405 --- [qtp898350050-174] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:13:44.414+07:00  INFO 24405 --- [qtp898350050-106] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-10T15:14:02.744+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:14:57.869+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 2
2025-09-10T15:14:57.904+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:15:05.926+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:15:05.929+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-10T15:15:05.930+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 10/09/2025@15:15:05+0700
2025-09-10T15:15:05.967+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 10/09/2025@15:15:00+0700 to 10/09/2025@15:30:00+0700
2025-09-10T15:15:05.968+07:00  INFO 24405 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 10/09/2025@15:15:00+0700 to 10/09/2025@15:30:00+0700
2025-09-10T15:15:05.968+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T15:16:02.060+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:16:58.180+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-10T15:16:58.195+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:17:05.220+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:17:17.027+07:00  INFO 24405 --- [qtp898350050-243] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:17:17.027+07:00  INFO 24405 --- [qtp898350050-239] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:17:17.045+07:00  INFO 24405 --- [qtp898350050-243] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:17:17.045+07:00  INFO 24405 --- [qtp898350050-239] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:18:06.302+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:18:57.418+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-10T15:18:57.441+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:19:04.456+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:20:06.552+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:20:06.555+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T15:20:56.691+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 7
2025-09-10T15:20:56.697+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:21:03.710+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:22:06.823+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:22:55.897+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:22:55.900+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:23:02.913+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:24:06.012+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:24:55.115+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-10T15:24:55.130+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:25:02.146+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:25:02.148+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-10T15:26:05.257+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:26:54.340+07:00  INFO 24405 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-10T15:26:54.346+07:00  INFO 24405 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-10T15:27:06.364+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:27:28.211+07:00  INFO 24405 --- [qtp898350050-172] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:27:28.211+07:00  INFO 24405 --- [qtp898350050-243] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:27:28.322+07:00  INFO 24405 --- [qtp898350050-243] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:27:28.326+07:00  INFO 24405 --- [qtp898350050-172] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:27:55.208+07:00  INFO 24405 --- [qtp898350050-243] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:27:55.216+07:00  INFO 24405 --- [qtp898350050-243] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:27:55.263+07:00  INFO 24405 --- [qtp898350050-427] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:27:55.279+07:00  INFO 24405 --- [qtp898350050-427] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:28:04.474+07:00  INFO 24405 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-10T15:28:09.233+07:00  INFO 24405 --- [qtp898350050-284] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:28:09.234+07:00  INFO 24405 --- [qtp898350050-422] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:28:09.238+07:00  INFO 24405 --- [qtp898350050-422] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:28:09.242+07:00  INFO 24405 --- [qtp898350050-284] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:28:20.193+07:00  INFO 24405 --- [qtp898350050-422] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:28:20.195+07:00  INFO 24405 --- [qtp898350050-284] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:28:20.200+07:00  INFO 24405 --- [qtp898350050-422] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:28:20.210+07:00  INFO 24405 --- [qtp898350050-284] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:28:27.958+07:00  INFO 24405 --- [qtp898350050-243] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:28:27.959+07:00  INFO 24405 --- [qtp898350050-172] n.d.module.session.ClientSessionManager  : Add a client session id = node0aquiowhng5xcujatf2lrc3xm0, token = 070fa2ee4bd6922fa75702d99b7be3ad
2025-09-10T15:28:27.965+07:00  INFO 24405 --- [qtp898350050-172] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:28:27.965+07:00  INFO 24405 --- [qtp898350050-243] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-10T15:28:31.780+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@543afa19{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-10T15:28:31.781+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-10T15:28:31.782+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-10T15:28:31.782+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-10T15:28:31.782+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-10T15:28:31.782+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-10T15:28:31.782+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-10T15:28:31.782+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-10T15:28:31.783+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-10T15:28:31.783+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-10T15:28:31.783+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-10T15:28:31.783+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-10T15:28:31.783+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-10T15:28:31.783+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-10T15:28:31.783+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-10T15:28:31.783+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-10T15:28:31.811+07:00  INFO 24405 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-10T15:28:31.882+07:00  INFO 24405 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-10T15:28:31.888+07:00  INFO 24405 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-10T15:28:31.910+07:00  INFO 24405 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-10T15:28:31.915+07:00  INFO 24405 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-10T15:28:31.924+07:00  INFO 24405 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-10T15:28:31.925+07:00  INFO 24405 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-10T15:28:31.926+07:00  INFO 24405 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-10T15:28:31.926+07:00  INFO 24405 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-10T15:28:31.926+07:00  INFO 24405 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-10T15:28:31.926+07:00  INFO 24405 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-10T15:28:31.927+07:00  INFO 24405 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-10T15:28:31.927+07:00  INFO 24405 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-10T15:28:31.927+07:00  INFO 24405 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-10T15:28:31.927+07:00  INFO 24405 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-10T15:28:31.927+07:00  INFO 24405 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-10T15:28:31.934+07:00  INFO 24405 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@40d1e561{STOPPING}[12.0.15,sto=0]
2025-09-10T15:28:31.938+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-10T15:28:31.940+07:00  INFO 24405 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@1c93f349{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13369059440323324654/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47620803{STOPPED}}
