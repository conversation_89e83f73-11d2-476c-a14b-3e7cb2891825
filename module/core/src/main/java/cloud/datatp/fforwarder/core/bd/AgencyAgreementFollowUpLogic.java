package cloud.datatp.fforwarder.core.bd;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fforwarder.core.bd.entity.AgencyAgreementFollowUp;
import cloud.datatp.fforwarder.core.bd.entity.AgencyAgreementFollowUp.Status;
import cloud.datatp.fforwarder.core.bd.repository.AgencyAgreementFollowUpRepository;
import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;

@Slf4j
@Component
public class AgencyAgreementFollowUpLogic extends CRMDaoService {
  
  @Autowired
  private AgencyAgreementFollowUpRepository repo;
  
  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;
  
  public AgencyAgreementFollowUp getById(ClientContext client, Long id) {
    return repo.getById(id);
  }
  
  public AgencyAgreementFollowUp save(ClientContext client, AgencyAgreementFollowUp followUp) {
    followUp.set(client);
    
    if (followUp.getHandledByAccountId() == null) {
      Account clientAccount = accountLogic.getAccountById(client, client.getAccountId());
      followUp.setHandledByAccountId(clientAccount.getId());
      followUp.setHandledByLabel(clientAccount.getFullName());
    }
    
    if (followUp.getDateCreated() == null) followUp.setDateCreated(new Date());
    boolean isNew = followUp.isNew();
    boolean hasNotificationTime = followUp.getNotificationTime() != null;
    boolean autoReminder = followUp.isSendingEmail();
    AgencyAgreementFollowUp saved = repo.save(followUp);
    if (autoReminder && hasNotificationTime) {
      if(isNew) {
        CRMMessageSystem crmMessageSystem = saved.toReminderMessage(client);
        crmMessageLogic.scheduleMessage(client, crmMessageSystem);
      } else {
        //TODO:
      }
    }
    return saved;
  }
  
  public boolean changeAgencyAgreementFollowUpStatus(ClientContext client, Long id, Status newStatus) {
    AgencyAgreementFollowUp followUp = getById(client, id);
    Objects.assertNotNull(followUp, "AgencyAgreementFollowUp is not found by id = {}", id);
    
    followUp.setStatus(newStatus);
    followUp.set(client);
    repo.save(followUp);
    return true;
  }

  public List<SqlMapRecord> searchAgencyAgreementFollowUps(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    try {
      sqlParams.addParam("accessAccountId", client.getAccountId());
      
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/BDSql.groovy";
      String scriptName = "SearchAgencyAgreementFollowUp";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(crmDataSource, sqlParams);
      return view.renameColumWithJavaConvention().getSqlMapRecords();
    } catch (Exception e) {
      log.error("Error when search Agency Agreement Follow Up", e);
      return new ArrayList<>();
    }
  }
  
  public boolean deleteByIds(ClientContext client, List<Long> ids) {
    repo.deleteAllById(ids);
    return true;
  }
  
  public boolean updateStorageState(ClientContext client, ChangeStorageStateRequest req) {
    repo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
}