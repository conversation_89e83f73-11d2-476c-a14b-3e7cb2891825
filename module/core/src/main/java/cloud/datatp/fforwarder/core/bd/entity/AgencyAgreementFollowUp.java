package cloud.datatp.fforwarder.core.bd.entity;

import java.io.Serial;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import cloud.datatp.fforwarder.core.bd.AgencyAgreementFollowUpMessagePlugin;
import cloud.datatp.fforwarder.core.bd.MailMessageReminderTemplate;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.security.client.ClientContext;
import net.datatp.util.bean.BeanUtil;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;

/**
 * <AUTHOR>
 */
@Entity
@Table(
  name = AgencyAgreementFollowUp.TABLE_NAME,
  indexes = {
    @Index(
      name = AgencyAgreementFollowUp.TABLE_NAME + "_agent_code_idx",
      columnList = "agent_code"
    ),
    @Index(
      name = AgencyAgreementFollowUp.TABLE_NAME + "_status_idx",
      columnList = "status"
    ),
  }
)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class AgencyAgreementFollowUp extends PersistableEntity<Long> {

  @Serial
  private static final long serialVersionUID = 1L;

  final static public String TABLE_NAME = "lgc_forwarder_crm_agency_agreement_follow_up";

  public enum Status {
    ON_PROCESS, FINISH, PENDING;

    public static Status parse(String token) {
      if (token == null)
        return ON_PROCESS;
      try {
        return valueOf(token.trim().toUpperCase());
      } catch (IllegalArgumentException e) {
        return ON_PROCESS;
      }
    }

    public String getLabel() {
      return switch (this) {
        case ON_PROCESS -> "On Process";
        case PENDING -> "Pending";
        case FINISH -> "Finish";
      };
    }
  }

  @Enumerated(EnumType.STRING)
  @Column(name = "status")
  private Status status = Status.ON_PROCESS;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_created")
  private Date dateCreated = new Date();

  @Column(name = "agent_code", nullable = true)
  private String agentCode;

  @Column(name = "agent_name", length = 1024)
  private String agentName;

  @Column(length = 9 * 1024)
  private String address;

  @Column(name = "country_label")
  private String countryLabel;

  @Column(name = "country_id")
  private Long countryId;

  @Column(name = "member_of_network")
  private String memberOfNetwork;

  @Column(name = "credit_amount")
  private double creditAmount;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "signed_date")
  private Date signedDate;

  @Column(name = "form_agreement_given_by")
  private String formAgreementGivenBy;

  @Column(name = "handled_by_account_id")
  private Long handledByAccountId;

  @Transient
  private String handledByEmail;

  @Column(name = "handled_by_label")
  private String handledByLabel;

  @Column(name = "subject_mail", length = 2048)
  private String subjectMail;

  @Column(name = "note", length = 9 * 1024)
  private String note;

  @Column(name = "sending_email")
  private boolean sendingEmail = false;

  @Column(name = "notification_time")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date notificationTime;

  @Column(name = "send_to_emails", length = 1024 * 2)
  @Convert(converter = StringSetConverter.class)
  private Set<SendToEmail> sendToEmails = new HashSet<>();

  public AgencyAgreementFollowUp mergeFromMapObject(MapObject mapObject) {
    List<String> fields = List.of(
        "status",
        "dateCreated", "agentCode", "agentName",
        "address", "countryLabel", "countryId", "memberOfNetwork", "creditAmount",
        "signedDate", "formAgreementGivenBy", "handledByAccountId", "handledByLabel",
        "subjectMail", "note", "notificationTime", "sendingEmail"
      );

    BeanUtil.updateFieldsFromMap(this, mapObject, fields);

    return this;
  }

  public CRMMessageSystem toReminderMessage(ClientContext client) {
    Objects.assertNotNull(!isNew(), "New Agency Agreement Follow Up cannot be send message to handler");
    CRMMessageSystem msg = new CRMMessageSystem();
    msg.setContent(MailMessageReminderTemplate.buildAgencyAgreementFollowUpMailMsg(this));
    msg.setScheduledAt(notificationTime != null ? notificationTime : new Date());
    msg.setMessageType(MessageType.MAIL);
    msg.setReferenceId(id);
    msg.setReferenceType(TABLE_NAME);
    msg.setPluginName(AgencyAgreementFollowUpMessagePlugin.PLUGIN_TYPE);
    msg.setRecipients(new HashSet<>(Arrays.asList("<EMAIL>")));
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", "<EMAIL>");
    metadata.put("subject", "CRM - Notification Message");
    metadata.put("to", Arrays.asList("<EMAIL>"));
    metadata.put("ccList", Arrays.asList("<EMAIL>"));
    msg.setMetadata(metadata);    return msg;
  }

  @Getter @Setter @NoArgsConstructor
  private class SendToEmail {
    private String fullName;
    private String email;
  }

}