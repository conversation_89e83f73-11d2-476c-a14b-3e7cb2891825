package cloud.datatp.fforwarder.core.bd;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fforwarder.core.bd.entity.AnnualConference;
import cloud.datatp.fforwarder.core.bd.repository.AnnualConferenceRepository;
import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;

@Slf4j
@Component
public class AnnualConferenceLogic extends CRMDaoService {

  @Autowired
  private AnnualConferenceRepository repo;
  
  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  public AnnualConference getById(ClientContext client, Long id) {
    return repo.getById(id);
  }
  
  public AnnualConference save(ClientContext client, AnnualConference annualConference) {
    annualConference.set(client);
    
    if (annualConference.getDateCreated() == null) annualConference.setDateCreated(new Date());
    
    if (annualConference.getInputByAccountId() == null) {
      Account clientAcc = accountLogic.getAccountById(client, client.getAccountId());
      annualConference.setInputByAccountId(clientAcc.getId());
      annualConference.setInputByAccountLabel(clientAcc.getFullName());
    }

    boolean isNew = annualConference.isNew();
    boolean hasNotificationTime = annualConference.getNotificationTime() != null;
    boolean autoReminder = annualConference.isAutoReminder();

    AnnualConference saved = repo.save(annualConference);
    if (autoReminder && hasNotificationTime) {
      if(isNew) {
        CRMMessageSystem crmMessageSystem = saved.toReminderMessage(client);
        crmMessageLogic.scheduleMessage(client, crmMessageSystem);
      } else {
        //TODO:
      }
    }

    return saved;
  }
  
  public List<SqlMapRecord> searchAnnualConferences(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    try {
      sqlParams.addParam("accessAccountId", client.getAccountId());
      
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/BDSql.groovy";
      String scriptName = "SearchAnnualConference";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(crmDataSource, sqlParams);
      return view.renameColumWithJavaConvention().getSqlMapRecords();
    } catch (Exception e) {
      log.error("Error when search Annual Conference", e);
      return new ArrayList<>();
    }
  }
  
  public boolean deleteByIds(ClientContext client, List<Long> ids) {
    repo.deleteAllById(ids);
    return true;
  }
  
  public boolean updateStorageState(ClientContext client, ChangeStorageStateRequest req) {
    repo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
}